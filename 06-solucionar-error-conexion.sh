#!/bin/bash

# 06-solucionar-error-conexion.sh
# Script para solucionar problemas de conexión con servidores RustDesk
# Error: Failed host lookup: 'admin.rustdesk.com'

echo "=== SOLUCIONADOR DE ERRORES DE CONEXIÓN RUSTDESK ==="
echo "Solucionando: Failed host lookup: 'admin.rustdesk.com'"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar conectividad básica
print_status "Verificando conectividad a internet..."
if ping -c 3 ******* > /dev/null 2>&1; then
    print_success "Conectividad a internet: OK"
else
    print_error "Sin conectividad a internet"
    exit 1
fi

# 2. Verificar resolución DNS
print_status "Verificando resolución DNS..."
if nslookup admin.rustdesk.com > /dev/null 2>&1; then
    print_success "DNS funciona correctamente"
else
    print_warning "Problema con resolución DNS"
    
    print_status "Configurando DNS alternativo..."
    # Backup del archivo original
    sudo cp /etc/resolv.conf /etc/resolv.conf.backup
    
    # Configurar DNS de Google y Cloudflare
    echo "# DNS configurado por script de RustDesk" | sudo tee /etc/resolv.conf > /dev/null
    echo "nameserver *******" | sudo tee -a /etc/resolv.conf > /dev/null
    echo "nameserver *******" | sudo tee -a /etc/resolv.conf > /dev/null
    echo "nameserver *******" | sudo tee -a /etc/resolv.conf > /dev/null
    echo "nameserver *******" | sudo tee -a /etc/resolv.conf > /dev/null
    
    print_success "DNS alternativo configurado"
fi

# 3. Probar conectividad a servidores RustDesk
print_status "Probando conectividad a servidores RustDesk..."

SERVERS=(
    "admin.rustdesk.com"
    "relay.rustdesk.com" 
    "rs-ny.rustdesk.com"
    "rs-sg.rustdesk.com"
)

WORKING_SERVERS=()

for server in "${SERVERS[@]}"; do
    if curl -s --connect-timeout 5 "https://$server" > /dev/null 2>&1; then
        print_success "✓ $server: Accesible"
        WORKING_SERVERS+=("$server")
    else
        print_warning "✗ $server: No accesible"
    fi
done

# 4. Configurar servidores alternativos si es necesario
if [ ${#WORKING_SERVERS[@]} -eq 0 ]; then
    print_warning "Ningún servidor oficial accesible. Configurando servidores alternativos..."
    
    # Detener RustDesk
    killall rustdesk 2>/dev/null || true
    
    # Configurar servidores alternativos
    mkdir -p ~/.config/rustdesk
    
    cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk con servidores alternativos
# Generada automáticamente para solucionar problemas de conexión

[options]
# === SERVIDORES ALTERNATIVOS ===
# Usar servidores públicos alternativos
custom-rendezvous-server = "rs-ny.rustdesk.com"
relay-server = "rs-ny.rustdesk.com"
api-server = ""

# === CONFIGURACIÓN DE RED ===
enable-lan-discovery = false
enable-direct-ip-access = true
enable-tunnel = true
enable-tcp = true
enable-udp = true

# === OPTIMIZACIONES DE RENDIMIENTO ===
enable-hardware-codec = true
enable-gpu = true
video-codec = "VP9"
audio-codec = "Opus"
image-quality = "Balanced"

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# === CONFIGURACIÓN DE INTERFAZ ===
theme = "dark"
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
enable-minimize-to-tray = true

# === LOGGING ===
log-level = "warn"
enable-file-logging = false

# === CONFIGURACIONES ESPECÍFICAS PARA REMOTO ===
enable-lan-discovery = false
enable-upnp = false
enable-local-relay = false
enable-direct-relay = true
enable-tcp-tunneling = true
EOF

    print_success "Configuración con servidores alternativos aplicada"
fi

# 5. Verificar y configurar firewall
print_status "Verificando configuración de firewall..."

if systemctl is-active --quiet ufw; then
    print_status "Configurando reglas UFW para RustDesk..."
    sudo ufw allow out 21115:21119/tcp comment "RustDesk TCP out"
    sudo ufw allow out 21116/udp comment "RustDesk UDP out"
    sudo ufw allow out 443/tcp comment "HTTPS out"
    sudo ufw allow out 80/tcp comment "HTTP out"
    print_success "Reglas de firewall configuradas"
fi

# 6. Limpiar cache DNS
print_status "Limpiando cache DNS..."
sudo systemctl restart systemd-resolved 2>/dev/null || true
sudo systemctl flush-dns 2>/dev/null || true
print_success "Cache DNS limpiado"

# 7. Crear script de prueba de conectividad
print_status "Creando script de prueba de conectividad..."
cat > ~/.local/bin/rustdesk-test-connection << 'EOF'
#!/bin/bash
# Script para probar conectividad de RustDesk

echo "=== PRUEBA DE CONECTIVIDAD RUSTDESK ==="
echo ""

echo "1. Conectividad básica:"
if ping -c 2 ******* > /dev/null 2>&1; then
    echo "   ✓ Internet: OK"
else
    echo "   ✗ Internet: FALLO"
fi

echo ""
echo "2. Resolución DNS:"
SERVERS=("admin.rustdesk.com" "relay.rustdesk.com" "rs-ny.rustdesk.com")
for server in "${SERVERS[@]}"; do
    if nslookup "$server" > /dev/null 2>&1; then
        echo "   ✓ $server: Resuelve"
    else
        echo "   ✗ $server: No resuelve"
    fi
done

echo ""
echo "3. Conectividad HTTPS:"
for server in "${SERVERS[@]}"; do
    if curl -s --connect-timeout 5 "https://$server" > /dev/null 2>&1; then
        echo "   ✓ $server: Accesible"
    else
        echo "   ✗ $server: No accesible"
    fi
done

echo ""
echo "4. Puertos RustDesk:"
netstat -tuln | grep -E "(21115|21116|21117|21118|21119)" | head -5

echo ""
echo "=== FIN PRUEBA ==="
EOF

chmod +x ~/.local/bin/rustdesk-test-connection

# 8. Información final
print_success "Solución de problemas completada"
echo ""
echo "=== ACCIONES REALIZADAS ==="
echo "✓ Verificación de conectividad"
echo "✓ Configuración de DNS alternativo"
echo "✓ Prueba de servidores RustDesk"
echo "✓ Configuración de firewall"
echo "✓ Limpieza de cache DNS"
echo "✓ Script de prueba creado"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo "1. Reinicia RustDesk:"
echo "   killall rustdesk && rustdesk"
echo ""
echo "2. Prueba la conectividad:"
echo "   rustdesk-test-connection"
echo ""
echo "3. Si el problema persiste:"
echo "   - Verifica tu conexión a internet"
echo "   - Contacta a tu ISP sobre bloqueos"
echo "   - Considera usar VPN"
echo ""
echo "=== ALTERNATIVAS SI NO FUNCIONA ==="
echo "1. Usar RustDesk sin cuenta (método tradicional)"
echo "2. Configurar servidor propio de RustDesk"
echo "3. Usar VPN para acceder a servidores"
echo ""
print_success "¡Reinicia RustDesk para aplicar los cambios!"
