#!/bin/bash

# 08-configurar-rustdesk-tradicional.sh
# Script para configurar RustDesk en modo tradicional (sin servidores oficiales)
# Ideal cuando los servidores están bloqueados

echo "=== CONFIGURACIÓN RUSTDESK TRADICIONAL ==="
echo "Configurando RustDesk para uso sin servidores oficiales"
echo "Ideal cuando admin.rustdesk.com está bloqueado"
echo ""
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Detener RustDesk si está ejecutándose
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true

# 2. Configurar RustDesk para modo tradicional
print_header "CONFIGURACIÓN TRADICIONAL"
print_status "Configurando RustDesk para uso sin servidores oficiales..."

mkdir -p ~/.config/rustdesk

cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk TRADICIONAL
# Optimizada para uso sin servidores oficiales
# Generada automáticamente

[options]
# === CONFIGURACIÓN DE RED ===
# NO usar servidores externos
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# Habilitar conexiones directas
enable-direct-ip-access = true
enable-lan-discovery = false
enable-tunnel = false
enable-tcp = true
enable-udp = true

# === OPTIMIZACIONES DE RENDIMIENTO ===
# Habilitar aceleración por hardware
enable-hardware-codec = true
enable-gpu = true

# Códecs optimizados
video-codec = "VP9"
audio-codec = "Opus"
image-quality = "Balanced"

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# === CONFIGURACIÓN DE INTERFAZ ===
theme = "dark"
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
enable-minimize-to-tray = true

# === LOGGING ===
log-level = "warn"
enable-file-logging = false

# === CONFIGURACIONES PARA CONEXIÓN DIRECTA ===
# Optimizar para conexiones P2P directas
enable-upnp = true
enable-local-relay = false
enable-direct-relay = true
enable-tcp-tunneling = false
EOF

print_success "Configuración tradicional aplicada"

# 3. Obtener información del sistema
print_header "INFORMACIÓN DEL SISTEMA"

# IP local
LOCAL_IP=$(ip route get ******* | awk '{print $7; exit}')
print_status "IP local: $LOCAL_IP"

# IP pública
print_status "Obteniendo IP pública..."
PUBLIC_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null || echo "No disponible")
print_status "IP pública: $PUBLIC_IP"

# Gateway (router)
GATEWAY=$(ip route | grep default | awk '{print $3}')
print_status "Gateway (router): $GATEWAY"

# 4. Crear scripts de ayuda
print_header "CREANDO SCRIPTS DE AYUDA"

# Script para mostrar información de conexión
cat > ~/.local/bin/rustdesk-info << EOF
#!/bin/bash
# Script para mostrar información de conexión RustDesk

echo "=== INFORMACIÓN RUSTDESK ==="
echo ""
echo "🔧 CONFIGURACIÓN ACTUAL:"
echo "   Modo: Tradicional (sin servidores oficiales)"
echo "   IP Local: $LOCAL_IP"
echo "   IP Pública: $PUBLIC_IP"
echo "   Gateway: $GATEWAY"
echo ""
echo "📋 PARA CONECTARTE DESDE OTRO EQUIPO:"
echo "   1. Instala RustDesk en el otro equipo"
echo "   2. Usa el ID que aparece en RustDesk"
echo "   3. Introduce la contraseña configurada"
echo ""
echo "🌐 CONFIGURACIÓN DE ROUTER NECESARIA:"
echo "   Protocolo: TCP"
echo "   Puertos externos: 21115-21119"
echo "   IP interna: $LOCAL_IP"
echo "   Puertos internos: 21115-21119"
echo ""
echo "   Protocolo: UDP"
echo "   Puerto externo: 21116"
echo "   IP interna: $LOCAL_IP"
echo "   Puerto interno: 21116"
echo ""
echo "🔗 ACCESO AL ROUTER:"
echo "   URL: http://$GATEWAY"
echo "   (Busca 'Port Forwarding' o 'Redirección de puertos')"
echo ""
echo "=== FIN INFORMACIÓN ==="
EOF

chmod +x ~/.local/bin/rustdesk-info

# Script para configurar router automáticamente (si es compatible)
cat > ~/.local/bin/rustdesk-setup-router << 'EOF'
#!/bin/bash
# Script para intentar configurar router automáticamente

echo "=== CONFIGURACIÓN AUTOMÁTICA DE ROUTER ==="
echo ""
echo "⚠️  ADVERTENCIA:"
echo "Este script intentará configurar tu router automáticamente"
echo "usando UPnP. No todos los routers son compatibles."
echo ""
echo "¿Deseas continuar? (y/n)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "Instalando herramientas UPnP..."
    
    # Instalar miniupnpc si no está disponible
    if ! command -v upnpc &> /dev/null; then
        sudo pacman -S --needed miniupnpc --noconfirm
    fi
    
    echo "Configurando puertos..."
    
    # Configurar puertos TCP
    for port in 21115 21116 21117 21118 21119; do
        upnpc -a $(ip route get ******* | awk '{print $7; exit}') $port $port TCP 2>/dev/null && echo "✓ Puerto TCP $port configurado" || echo "✗ Error en puerto TCP $port"
    done
    
    # Configurar puerto UDP
    upnpc -a $(ip route get ******* | awk '{print $7; exit}') 21116 21116 UDP 2>/dev/null && echo "✓ Puerto UDP 21116 configurado" || echo "✗ Error en puerto UDP 21116"
    
    echo ""
    echo "Configuración automática completada."
    echo "Si hubo errores, configura manualmente desde el router."
else
    echo "Configuración automática cancelada."
fi
EOF

chmod +x ~/.local/bin/rustdesk-setup-router

print_success "Scripts de ayuda creados"

# 5. Iniciar RustDesk
print_header "INICIANDO RUSTDESK"
print_status "Iniciando RustDesk en modo tradicional..."

# Iniciar RustDesk en background
rustdesk &
RUSTDESK_PID=$!

sleep 3

if ps -p $RUSTDESK_PID > /dev/null; then
    print_success "RustDesk iniciado correctamente"
else
    print_warning "RustDesk puede haber tenido problemas al iniciar"
fi

# 6. Información final
print_header "CONFIGURACIÓN COMPLETADA"
echo ""
print_success "¡RustDesk configurado en modo tradicional!"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo ""
echo "1. 📋 VER INFORMACIÓN DE CONEXIÓN:"
echo "   rustdesk-info"
echo ""
echo "2. 🔧 CONFIGURAR ROUTER:"
echo "   Opción A (Automática): rustdesk-setup-router"
echo "   Opción B (Manual): Accede a http://$GATEWAY"
echo ""
echo "3. 📝 EN RUSTDESK:"
echo "   - Anota tu ID (aparece en pantalla principal)"
echo "   - Configura contraseña fija en Configuración → Seguridad"
echo "   - Desmarca 'Contraseña temporal'"
echo ""
echo "4. 🌐 CONECTAR DESDE OTRO EQUIPO:"
echo "   - Instala RustDesk"
echo "   - Usa tu ID + contraseña"
echo ""
echo "=== COMANDOS ÚTILES ==="
echo "• rustdesk-info           : Ver información de conexión"
echo "• rustdesk-setup-router   : Configurar router automáticamente"
echo "• rustdesk-diagnostics    : Diagnóstico general"
echo ""
echo "=== VENTAJAS DEL MODO TRADICIONAL ==="
echo "✅ No depende de servidores externos"
echo "✅ Conexión directa (mejor rendimiento)"
echo "✅ Mayor privacidad"
echo "✅ Funciona aunque estén bloqueados los servidores oficiales"
echo ""
print_success "¡RustDesk listo para uso remoto tradicional!"
echo ""
print_warning "IMPORTANTE: Configura el router para acceso desde internet"
