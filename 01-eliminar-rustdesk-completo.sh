#!/bin/bash

# 01-eliminar-rustdesk-completo.sh
# Script para eliminar completamente RustDesk de Manjaro Plasma
# Elimina paquetes, configuraciones, datos de usuario y servicios

echo "=== ELIMINACIÓN COMPLETA DE RUSTDESK ==="
echo "Este script eliminará completamente RustDesk del sistema"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Detener todos los servicios de RustDesk
print_status "Deteniendo servicios de RustDesk..."
sudo systemctl stop rustdesk 2>/dev/null || true
sudo systemctl disable rustdesk 2>/dev/null || true
killall rustdesk 2>/dev/null || true
killall rustdesk-server 2>/dev/null || true

# 2. Eliminar paquetes instalados
print_status "Eliminando paquetes de RustDesk..."

# Verificar si está instalado desde AUR
if pacman -Qi rustdesk &>/dev/null; then
    print_status "Eliminando RustDesk instalado desde AUR..."
    yay -Rns rustdesk --noconfirm 2>/dev/null || sudo pacman -Rns rustdesk --noconfirm 2>/dev/null || true
fi

# Verificar si está instalado desde Flatpak
if flatpak list | grep -q rustdesk; then
    print_status "Eliminando RustDesk de Flatpak..."
    flatpak uninstall com.rustdesk.RustDesk --delete-data -y 2>/dev/null || true
fi

# Verificar si está instalado desde Snap
if snap list | grep -q rustdesk; then
    print_status "Eliminando RustDesk de Snap..."
    sudo snap remove rustdesk 2>/dev/null || true
fi

# 3. Eliminar archivos de configuración del sistema
print_status "Eliminando archivos de configuración del sistema..."
sudo rm -rf /etc/rustdesk/ 2>/dev/null || true
sudo rm -rf /usr/share/rustdesk/ 2>/dev/null || true
sudo rm -rf /opt/rustdesk/ 2>/dev/null || true
sudo rm -f /usr/bin/rustdesk 2>/dev/null || true
sudo rm -f /usr/local/bin/rustdesk 2>/dev/null || true

# 4. Eliminar archivos de servicio systemd
print_status "Eliminando servicios systemd..."
sudo rm -f /etc/systemd/system/rustdesk.service 2>/dev/null || true
sudo rm -f /usr/lib/systemd/system/rustdesk.service 2>/dev/null || true
sudo rm -f /lib/systemd/system/rustdesk.service 2>/dev/null || true
sudo systemctl daemon-reload

# 5. Eliminar archivos de usuario
print_status "Eliminando configuraciones de usuario..."
rm -rf ~/.config/rustdesk/ 2>/dev/null || true
rm -rf ~/.local/share/rustdesk/ 2>/dev/null || true
rm -rf ~/.cache/rustdesk/ 2>/dev/null || true
rm -rf ~/RustDesk/ 2>/dev/null || true

# 6. Eliminar archivos .desktop
print_status "Eliminando accesos directos..."
rm -f ~/.local/share/applications/rustdesk.desktop 2>/dev/null || true
sudo rm -f /usr/share/applications/rustdesk.desktop 2>/dev/null || true

# 7. Eliminar logs
print_status "Eliminando logs..."
sudo rm -rf /var/log/rustdesk/ 2>/dev/null || true
rm -rf ~/.local/share/rustdesk/log/ 2>/dev/null || true

# 8. Limpiar registros de autostart
print_status "Limpiando autostart..."
rm -f ~/.config/autostart/rustdesk.desktop 2>/dev/null || true

# 9. Verificar eliminación
print_status "Verificando eliminación..."
FOUND_FILES=0

# Buscar archivos restantes
if find /usr -name "*rustdesk*" 2>/dev/null | grep -q .; then
    print_warning "Archivos encontrados en /usr:"
    find /usr -name "*rustdesk*" 2>/dev/null
    FOUND_FILES=1
fi

if find /opt -name "*rustdesk*" 2>/dev/null | grep -q .; then
    print_warning "Archivos encontrados en /opt:"
    find /opt -name "*rustdesk*" 2>/dev/null
    FOUND_FILES=1
fi

if find /etc -name "*rustdesk*" 2>/dev/null | grep -q .; then
    print_warning "Archivos encontrados en /etc:"
    find /etc -name "*rustdesk*" 2>/dev/null
    FOUND_FILES=1
fi

if find ~ -name "*rustdesk*" 2>/dev/null | grep -q .; then
    print_warning "Archivos encontrados en home:"
    find ~ -name "*rustdesk*" 2>/dev/null
    FOUND_FILES=1
fi

# 10. Limpiar cache de paquetes
print_status "Limpiando cache..."
yay -Sc --noconfirm 2>/dev/null || sudo pacman -Sc --noconfirm 2>/dev/null || true

if [ $FOUND_FILES -eq 0 ]; then
    print_success "RustDesk eliminado completamente del sistema"
else
    print_warning "Se encontraron algunos archivos residuales (mostrados arriba)"
    echo "¿Deseas eliminarlos manualmente? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_status "Eliminando archivos residuales..."
        sudo find /usr -name "*rustdesk*" -delete 2>/dev/null || true
        sudo find /opt -name "*rustdesk*" -delete 2>/dev/null || true
        sudo find /etc -name "*rustdesk*" -delete 2>/dev/null || true
        find ~ -name "*rustdesk*" -delete 2>/dev/null || true
        print_success "Archivos residuales eliminados"
    fi
fi

print_success "Proceso de eliminación completado"
print_status "Puedes proceder con la instalación limpia usando el siguiente script"
