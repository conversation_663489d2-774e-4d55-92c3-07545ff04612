#!/bin/bash

# 13-instalar-configurar-tailscale.sh
# Script para instalar y configurar Tailscale en Manjaro Plasma
# Crea una VPN personal para acceso remoto a RustDesk

echo "=== INSTALACIÓN Y CONFIGURACIÓN DE TAILSCALE ==="
echo "Tailscale creará una red privada virtual para acceso remoto"
echo "Perfecto para RustDesk sin configurar router"
echo ""
echo "Beneficios:"
echo "✅ Gratuito hasta 20 dispositivos"
echo "✅ Conexión permanente y segura"
echo "✅ IPs fijas para cada dispositivo"
echo "✅ Funciona en móviles también"
echo "✅ Muy fácil de usar"
echo ""
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar conectividad
print_header "VERIFICACIÓN INICIAL"
print_status "Verificando conectividad a internet..."
if ping -c 3 ******* > /dev/null 2>&1; then
    print_success "Conectividad: OK"
else
    print_error "Sin conectividad a internet. Verifica tu conexión."
    exit 1
fi

# 2. Instalar Tailscale
print_header "INSTALACIÓN DE TAILSCALE"
print_status "Instalando Tailscale desde AUR..."

if command -v tailscale &> /dev/null; then
    print_success "Tailscale ya está instalado"
else
    print_status "Descargando e instalando Tailscale..."
    yay -S tailscale --noconfirm
    
    if [ $? -eq 0 ]; then
        print_success "Tailscale instalado correctamente"
    else
        print_error "Error instalando Tailscale"
        exit 1
    fi
fi

# 3. Habilitar y iniciar servicio
print_header "CONFIGURACIÓN DEL SERVICIO"
print_status "Habilitando servicio tailscaled..."
sudo systemctl enable tailscaled

print_status "Iniciando servicio tailscaled..."
sudo systemctl start tailscaled

# Verificar que el servicio esté funcionando
if systemctl is-active --quiet tailscaled; then
    print_success "Servicio tailscaled funcionando correctamente"
else
    print_error "Error iniciando el servicio tailscaled"
    exit 1
fi

# 4. Conectar a Tailscale
print_header "CONEXIÓN A TAILSCALE"
print_status "Conectando este equipo a tu red Tailscale..."
echo ""
print_warning "IMPORTANTE: Se abrirá tu navegador para autenticarte"
print_warning "Necesitarás crear una cuenta gratuita en Tailscale si no tienes una"
echo ""
echo "Presiona Enter cuando estés listo para continuar..."
read

# Conectar a Tailscale
sudo tailscale up

if [ $? -eq 0 ]; then
    print_success "Conexión a Tailscale exitosa"
else
    print_error "Error conectando a Tailscale"
    exit 1
fi

# 5. Obtener información de la red Tailscale
print_header "INFORMACIÓN DE RED TAILSCALE"
print_status "Obteniendo información de tu red Tailscale..."

# Esperar un momento para que se establezca la conexión
sleep 3

TAILSCALE_IP=$(tailscale ip -4 2>/dev/null)
TAILSCALE_STATUS=$(tailscale status --json 2>/dev/null)

if [ -n "$TAILSCALE_IP" ]; then
    print_success "IP de Tailscale asignada: $TAILSCALE_IP"
else
    print_warning "IP de Tailscale no disponible aún (puede tomar unos segundos)"
fi

# 6. Configurar firewall para Tailscale
print_header "CONFIGURACIÓN DE FIREWALL"
print_status "Configurando firewall para Tailscale..."

if systemctl is-active --quiet ufw; then
    print_status "Configurando UFW para Tailscale..."
    
    # Permitir tráfico de Tailscale
    sudo ufw allow in on tailscale0
    sudo ufw allow out on tailscale0
    
    # Permitir RustDesk a través de Tailscale
    sudo ufw allow from **********/10 to any port 21115:21119 comment "RustDesk via Tailscale"
    
    print_success "Firewall configurado para Tailscale"
else
    print_status "UFW no está activo, saltando configuración de firewall"
fi

# 7. Crear scripts de ayuda para Tailscale
print_header "CREANDO SCRIPTS DE AYUDA"

# Script de información de Tailscale
cat > ~/.local/bin/tailscale-info << 'EOF'
#!/bin/bash
# Información de Tailscale

echo "=== INFORMACIÓN TAILSCALE ==="
echo ""

# Estado de Tailscale
if systemctl is-active --quiet tailscaled; then
    echo "🟢 Estado del servicio: ACTIVO"
else
    echo "🔴 Estado del servicio: INACTIVO"
    echo "   Ejecuta: sudo systemctl start tailscaled"
    exit 1
fi

# IP de Tailscale
TAILSCALE_IP=$(tailscale ip -4 2>/dev/null)
if [ -n "$TAILSCALE_IP" ]; then
    echo "🌐 Tu IP en Tailscale: $TAILSCALE_IP"
else
    echo "⚠️  IP de Tailscale no disponible"
    echo "   Ejecuta: sudo tailscale up"
fi

echo ""
echo "📱 DISPOSITIVOS EN TU RED TAILSCALE:"
tailscale status 2>/dev/null | grep -E "^[0-9]" | while read line; do
    echo "   ✓ $line"
done

echo ""
echo "🔧 PARA CONECTAR RUSTDESK:"
echo "   1. Instala RustDesk en otro dispositivo"
echo "   2. Conecta ese dispositivo a Tailscale también"
echo "   3. Usa tu ID de RustDesk normal"
echo "   4. ¡Funciona como si fuera red local!"
echo ""
echo "📲 INSTALAR EN OTROS DISPOSITIVOS:"
echo "   • Android/iOS: Busca 'Tailscale' en la tienda"
echo "   • Windows: https://tailscale.com/download/windows"
echo "   • macOS: https://tailscale.com/download/mac"
echo "   • Linux: https://tailscale.com/download/linux"
echo ""
echo "=== FIN INFORMACIÓN ==="
EOF

chmod +x ~/.local/bin/tailscale-info

# Script para gestionar Tailscale
cat > ~/.local/bin/tailscale-manager << 'EOF'
#!/bin/bash
# Gestor de Tailscale

echo "=== GESTOR TAILSCALE ==="
echo ""
echo "Selecciona una opción:"
echo "1) Ver estado e información"
echo "2) Conectar/reconectar"
echo "3) Desconectar"
echo "4) Ver dispositivos conectados"
echo "5) Salir"
echo ""
echo -n "Opción [1-5]: "
read -r option

case $option in
    1)
        echo ""
        tailscale-info
        ;;
    2)
        echo ""
        echo "Conectando a Tailscale..."
        sudo tailscale up
        echo "Conexión completada"
        ;;
    3)
        echo ""
        echo "Desconectando de Tailscale..."
        sudo tailscale down
        echo "Desconectado"
        ;;
    4)
        echo ""
        echo "Dispositivos en tu red Tailscale:"
        tailscale status
        ;;
    5)
        echo "Saliendo..."
        ;;
    *)
        echo "Opción inválida"
        ;;
esac
EOF

chmod +x ~/.local/bin/tailscale-manager

# Script para configurar RustDesk con Tailscale
cat > ~/.local/bin/rustdesk-tailscale-setup << 'EOF'
#!/bin/bash
# Configurar RustDesk para usar con Tailscale

echo "=== CONFIGURAR RUSTDESK CON TAILSCALE ==="
echo ""

# Verificar que Tailscale esté funcionando
if ! systemctl is-active --quiet tailscaled; then
    echo "❌ Tailscale no está funcionando"
    echo "Ejecuta: sudo systemctl start tailscaled"
    exit 1
fi

TAILSCALE_IP=$(tailscale ip -4 2>/dev/null)
if [ -z "$TAILSCALE_IP" ]; then
    echo "❌ No tienes IP de Tailscale"
    echo "Ejecuta: sudo tailscale up"
    exit 1
fi

echo "✅ Tailscale funcionando correctamente"
echo "🌐 Tu IP en Tailscale: $TAILSCALE_IP"
echo ""
echo "🔧 CONFIGURACIÓN PARA RUSTDESK:"
echo ""
echo "1. 📝 INFORMACIÓN DE CONEXIÓN:"
echo "   • Tu ID de RustDesk: (aparece en la ventana principal)"
echo "   • Tu IP de Tailscale: $TAILSCALE_IP"
echo "   • Contraseña: (la que configuraste en RustDesk)"
echo ""
echo "2. 📱 EN OTROS DISPOSITIVOS:"
echo "   a) Instala Tailscale y conéctalo a tu red"
echo "   b) Instala RustDesk"
echo "   c) Conecta usando tu ID de RustDesk normal"
echo "   d) ¡Funciona como red local!"
echo ""
echo "3. 🔍 VERIFICAR CONEXIÓN:"
echo "   • Desde otro dispositivo en Tailscale:"
echo "   • ping $TAILSCALE_IP"
echo "   • Si responde, RustDesk funcionará"
echo ""
echo "=== VENTAJAS CON TAILSCALE ==="
echo "✅ Acceso desde cualquier lugar del mundo"
echo "✅ Conexión segura y encriptada"
echo "✅ Sin configurar router"
echo "✅ IPs fijas para cada dispositivo"
echo "✅ Funciona en móviles también"
EOF

chmod +x ~/.local/bin/rustdesk-tailscale-setup

print_success "Scripts de ayuda creados"

# 8. Información final
print_header "CONFIGURACIÓN COMPLETADA"
echo ""
print_success "¡Tailscale instalado y configurado correctamente!"
echo ""

# Mostrar IP de Tailscale si está disponible
TAILSCALE_IP=$(tailscale ip -4 2>/dev/null)
if [ -n "$TAILSCALE_IP" ]; then
    echo "🌐 Tu IP en Tailscale: $TAILSCALE_IP"
else
    echo "⚠️  IP de Tailscale se asignará en unos segundos"
fi

echo ""
echo "=== PRÓXIMOS PASOS ==="
echo ""
echo "1. 📱 INSTALAR EN OTROS DISPOSITIVOS:"
echo "   • Android/iOS: Busca 'Tailscale' en la tienda"
echo "   • Windows: https://tailscale.com/download/windows"
echo "   • macOS: https://tailscale.com/download/mac"
echo "   • Otros Linux: https://tailscale.com/download/linux"
echo ""
echo "2. 🔧 CONFIGURAR RUSTDESK:"
echo "   rustdesk-tailscale-setup"
echo ""
echo "3. 📊 VER INFORMACIÓN:"
echo "   tailscale-info"
echo ""
echo "4. ⚙️  GESTIONAR TAILSCALE:"
echo "   tailscale-manager"
echo ""
echo "=== COMANDOS ÚTILES ==="
echo "• tailscale-info           : Ver información completa"
echo "• tailscale-manager        : Gestionar conexión"
echo "• rustdesk-tailscale-setup : Configurar RustDesk"
echo "• tailscale status         : Ver dispositivos conectados"
echo ""
echo "=== CÓMO FUNCIONA ==="
echo "1. Todos los dispositivos con Tailscale forman una red privada"
echo "2. Cada dispositivo tiene una IP fija (100.x.x.x)"
echo "3. RustDesk funciona como si fuera red local"
echo "4. Acceso seguro desde cualquier lugar del mundo"
echo ""
print_success "¡Tailscale listo para usar con RustDesk!"
echo ""
print_warning "IMPORTANTE: Instala Tailscale en los otros dispositivos que quieras conectar"
