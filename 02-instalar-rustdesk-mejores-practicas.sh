#!/bin/bash

# 02-instalar-rustdesk-mejores-practicas.sh
# Script para instalar RustDesk con las mejores prácticas en Manjaro Plasma
# Configurado para uso REMOTO únicamente

echo "=== INSTALACIÓN DE RUSTDESK CON MEJORES PRÁCTICAS ==="
echo "Este script instalará RustDesk optimizado para uso remoto"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar que yay esté disponible
if ! command -v yay &> /dev/null; then
    print_status "Instalando yay..."
    cd /tmp
    git clone https://aur.archlinux.org/yay.git
    cd yay
    makepkg -si --noconfirm
    cd ~
fi

# 2. Instalar dependencias necesarias
print_status "Instalando dependencias..."
sudo pacman -S --needed --noconfirm \
    base-devel \
    git \
    rust \
    cmake \
    clang \
    gtk3 \
    libxcb \
    alsa-lib \
    pulseaudio \
    gstreamer \
    gst-plugins-base \
    libvpx \
    opus 2>/dev/null || true

# 3. Elegir método de instalación
echo ""
echo "Selecciona el método de instalación:"
echo "1) AUR (recomendado para Manjaro)"
echo "2) Flatpak (más aislado)"
echo "3) AppImage (portable)"
echo -n "Opción [1-3]: "
read -r INSTALL_METHOD

case $INSTALL_METHOD in
    1)
        print_status "Instalando desde AUR..."
        yay -S rustdesk --noconfirm
        INSTALL_PATH="/usr/bin/rustdesk"
        ;;
    2)
        print_status "Instalando desde Flatpak..."
        sudo pacman -S --needed flatpak --noconfirm
        flatpak remote-add --if-not-exists flathub https://flathub.org/repo/flathub.flatpakrepo
        flatpak install flathub com.rustdesk.RustDesk -y
        INSTALL_PATH="flatpak run com.rustdesk.RustDesk"
        ;;
    3)
        print_status "Descargando AppImage..."
        mkdir -p ~/Applications
        cd ~/Applications
        wget -O rustdesk.AppImage "https://github.com/rustdesk/rustdesk/releases/latest/download/rustdesk-x86_64.AppImage"
        chmod +x rustdesk.AppImage
        INSTALL_PATH="$HOME/Applications/rustdesk.AppImage"

        # Crear acceso directo
        cat > ~/.local/share/applications/rustdesk.desktop << EOF
[Desktop Entry]
Name=RustDesk
Comment=Remote Desktop
Exec=$HOME/Applications/rustdesk.AppImage
Icon=rustdesk
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
EOF
        ;;
    *)
        print_error "Opción inválida"
        exit 1
        ;;
esac

# 4. Configurar firewall si está activo
if systemctl is-active --quiet ufw; then
    print_status "Configurando firewall UFW..."
    sudo ufw allow 21115:21119/tcp comment "RustDesk TCP"
    sudo ufw allow 21116/udp comment "RustDesk UDP"
elif systemctl is-active --quiet firewalld; then
    print_status "Configurando firewall FirewallD..."
    sudo firewall-cmd --permanent --add-port=21115-21119/tcp
    sudo firewall-cmd --permanent --add-port=21116/udp
    sudo firewall-cmd --reload
fi

print_success "Instalación completada"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo "1. Ejecuta: rustdesk"
echo "2. Configura tu ID y contraseña para acceso remoto"
echo "3. Usa el script 03-configurar-rustdesk-remoto.sh para configuración avanzada"
echo ""
print_success "¡RustDesk instalado con mejores prácticas!"

# 01-instalar-rustdesk-mejores-practicas.sh
# Script para instalar RustDesk con las mejores prácticas en Manjaro Plasma
# Configurado para uso REMOTO únicamente

echo "=== INSTALACIÓN DE RUSTDESK CON MEJORES PRÁCTICAS ==="
echo "Este script instalará RustDesk optimizado para uso remoto"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Actualizar sistema
print_status "Actualizando sistema..."
sudo pacman -Syu --noconfirm

# 2. Instalar dependencias necesarias
print_status "Instalando dependencias..."
sudo pacman -S --needed --noconfirm \
    base-devel \
    git \
    rust \
    cmake \
    clang \
    gtk3 \
    libxcb \
    libxrandr \
    libasound2-dev \
    libpulse-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libvpx-dev \
    libopus-dev \
    libyuv-dev 2>/dev/null || true

# Instalar yay si no está disponible
if ! command -v yay &> /dev/null; then
    print_status "Instalando yay..."
    cd /tmp
    git clone https://aur.archlinux.org/yay.git
    cd yay
    makepkg -si --noconfirm
    cd ~
fi

# 3. Elegir método de instalación
echo ""
echo "Selecciona el método de instalación:"
echo "1) AUR (recomendado para Manjaro)"
echo "2) Flatpak (más aislado)"
echo "3) AppImage (portable)"
echo -n "Opción [1-3]: "
read -r INSTALL_METHOD

case $INSTALL_METHOD in
    1)
        print_status "Instalando desde AUR..."
        yay -S rustdesk --noconfirm
        INSTALL_PATH="/usr/bin/rustdesk"
        ;;
    2)
        print_status "Instalando desde Flatpak..."
        sudo pacman -S --needed flatpak --noconfirm
        flatpak remote-add --if-not-exists flathub https://flathub.org/repo/flathub.flatpakrepo
        flatpak install flathub com.rustdesk.RustDesk -y
        INSTALL_PATH="flatpak run com.rustdesk.RustDesk"
        ;;
    3)
        print_status "Descargando AppImage..."
        mkdir -p ~/Applications
        cd ~/Applications
        wget -O rustdesk.AppImage "https://github.com/rustdesk/rustdesk/releases/latest/download/rustdesk-x86_64.AppImage"
        chmod +x rustdesk.AppImage
        INSTALL_PATH="$HOME/Applications/rustdesk.AppImage"
        
        # Crear acceso directo
        cat > ~/.local/share/applications/rustdesk.desktop << EOF
[Desktop Entry]
Name=RustDesk
Comment=Remote Desktop
Exec=$HOME/Applications/rustdesk.AppImage
Icon=rustdesk
Terminal=false
Type=Application
Categories=Network;RemoteAccess;
EOF
        ;;
    *)
        print_error "Opción inválida"
        exit 1
        ;;
esac

# 4. Configurar para uso remoto únicamente
print_status "Configurando para uso remoto..."

# Crear directorio de configuración
mkdir -p ~/.config/rustdesk

# Configuración optimizada para uso remoto
cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk optimizada para uso remoto
# Generada automáticamente

[options]
# Desactivar servidor local (solo cliente remoto)
enable-lan-discovery = false
enable-direct-ip-access = true

# Optimizaciones de rendimiento
enable-hardware-codec = true
enable-gpu = true
video-codec = "VP9"
audio-codec = "Opus"

# Configuración de red
enable-tunnel = true
enable-tcp = true
enable-udp = true

# Seguridad
enable-password = true
enable-2fa = false
enable-file-transfer = true
enable-clipboard = true

# UI optimizada
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
theme = "dark"

# Logging mínimo
log-level = "warn"
EOF

print_success "Configuración base creada"

# 5. Configurar firewall si está activo
if systemctl is-active --quiet ufw; then
    print_status "Configurando firewall UFW..."
    sudo ufw allow 21115:21119/tcp comment "RustDesk TCP"
    sudo ufw allow 21116/udp comment "RustDesk UDP"
elif systemctl is-active --quiet firewalld; then
    print_status "Configurando firewall FirewallD..."
    sudo firewall-cmd --permanent --add-port=21115-21119/tcp
    sudo firewall-cmd --permanent --add-port=21116/udp
    sudo firewall-cmd --reload
fi

# 6. Crear script de inicio optimizado
print_status "Creando script de inicio..."
cat > ~/.local/bin/rustdesk-remote << EOF
#!/bin/bash
# Script de inicio optimizado para RustDesk remoto

# Variables de entorno para mejor rendimiento
export RUSTDESK_LOG_LEVEL=warn
export RUSTDESK_DISABLE_LAN=1

# Iniciar RustDesk
$INSTALL_PATH --remote-only "\$@"
EOF

chmod +x ~/.local/bin/rustdesk-remote

# 7. Información sobre "Iniciar Sesión"
print_status "Creando guía sobre 'Iniciar Sesión'..."
cat > ~/rustdesk-login-info.txt << 'EOF'
=== ¿QUÉ ES "INICIAR SESIÓN" EN RUSTDESK? ===

La opción "Iniciar Sesión" en RustDesk se refiere a crear una cuenta en los servidores de RustDesk.

BENEFICIOS:
1. SINCRONIZACIÓN: Tus contactos y configuraciones se sincronizan entre dispositivos
2. ACCESO FÁCIL: Puedes acceder a tus equipos desde cualquier lugar sin recordar IDs
3. GESTIÓN CENTRALIZADA: Administra todos tus dispositivos desde una interfaz web
4. MEJOR RENDIMIENTO: Los servidores de RustDesk pueden optimizar las conexiones
5. FUNCIONES AVANZADAS: Acceso a funciones como grupos, permisos avanzados, etc.

DESVENTAJAS:
1. PRIVACIDAD: Tus datos pasan por servidores de terceros
2. DEPENDENCIA: Necesitas internet para gestionar dispositivos
3. CUENTA REQUERIDA: Debes crear y mantener una cuenta

RECOMENDACIÓN PARA TU CASO:
- SÍ úsala si quieres facilidad de uso y gestión centralizada
- NO la uses si prefieres máxima privacidad y control local

Para activarla: Abre RustDesk > Menú > "Iniciar Sesión" > Crear cuenta
EOF

print_success "Instalación completada"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo "1. Ejecuta: rustdesk o ~/.local/bin/rustdesk-remote"
echo "2. Lee: ~/rustdesk-login-info.txt para decidir sobre 'Iniciar Sesión'"
echo "3. Configura tu ID y contraseña para acceso remoto"
echo "4. Usa el script 02-configurar-rustdesk-remoto.sh para configuración avanzada"
echo ""
print_success "¡RustDesk instalado con mejores prácticas!"
