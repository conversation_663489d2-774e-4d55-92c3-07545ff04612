#!/bin/bash

# 11-configurar-rustdesk-red-local.sh
# Configuración optimizada para RustDesk sin acceso al router
# Enfocado en uso de red local y alternativas remotas

echo "=== CONFIGURACIÓN RUSTDESK SIN ROUTER ==="
echo "Optimizando RustDesk para uso sin acceso al router"
echo "Enfoque: Red local + alternativas remotas"
echo ""
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Detener RustDesk
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true

# 2. Configurar para uso local optimizado
print_header "CONFIGURACIÓN PARA RED LOCAL"
print_status "Optimizando configuración para red local..."

mkdir -p ~/.config/rustdesk

cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk optimizada para RED LOCAL
# Sin acceso al router - Enfoque en LAN y alternativas
# Generada automáticamente

[options]
# === CONFIGURACIÓN DE RED ===
# Optimizado para red local
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# Habilitar descubrimiento LAN para red local
enable-lan-discovery = true
enable-direct-ip-access = true
enable-tunnel = false
enable-tcp = true
enable-udp = true

# === OPTIMIZACIONES DE RENDIMIENTO ===
# Máximo rendimiento para red local
enable-hardware-codec = true
enable-gpu = true

# Códecs optimizados para LAN
video-codec = "VP9"
audio-codec = "Opus"
image-quality = "Best"  # Mejor calidad en LAN

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# === CONFIGURACIÓN DE INTERFAZ ===
theme = "dark"
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
enable-minimize-to-tray = true

# === LOGGING ===
log-level = "info"  # Más detallado para diagnóstico
enable-file-logging = true

# === CONFIGURACIONES PARA RED LOCAL ===
# Optimizar para conexiones LAN
enable-upnp = false  # No necesario sin router
enable-local-relay = true
enable-direct-relay = true
enable-tcp-tunneling = false
EOF

print_success "Configuración para red local aplicada"

# 3. Obtener información de red local
print_header "INFORMACIÓN DE RED LOCAL"

LOCAL_IP=$(ip route get ******* | awk '{print $7; exit}')
NETWORK=$(ip route | grep "$LOCAL_IP" | grep -v default | awk '{print $1}' | head -1)
GATEWAY=$(ip route | grep default | awk '{print $3}')

print_status "IP Local: $LOCAL_IP"
print_status "Red Local: $NETWORK"
print_status "Gateway: $GATEWAY"

# 4. Escanear otros equipos en la red
print_status "Escaneando equipos en la red local..."
if command -v nmap &> /dev/null; then
    DEVICES=$(nmap -sn "$NETWORK" 2>/dev/null | grep "Nmap scan report" | wc -l)
    print_status "Dispositivos encontrados en la red: $DEVICES"
else
    print_warning "nmap no disponible. Instalando..."
    sudo pacman -S --needed nmap --noconfirm
fi

# 5. Crear scripts específicos para red local
print_header "CREANDO SCRIPTS PARA RED LOCAL"

# Script de información local
cat > ~/.local/bin/rustdesk-local-info << EOF
#!/bin/bash
# Información de RustDesk para red local

echo "=== RUSTDESK - INFORMACIÓN RED LOCAL ==="
echo ""
echo "🏠 CONFIGURACIÓN ACTUAL:"
echo "   Modo: Red Local Optimizada"
echo "   IP Local: $LOCAL_IP"
echo "   Red: $NETWORK"
echo "   Gateway: $GATEWAY"
echo ""
echo "📱 PARA CONECTARTE DESDE OTRO EQUIPO EN LA MISMA RED:"
echo "   1. Instala RustDesk en el otro equipo"
echo "   2. Asegúrate de estar en la misma WiFi/red"
echo "   3. Usa el ID que aparece en RustDesk"
echo "   4. Introduce la contraseña configurada"
echo "   5. ¡Funciona sin configurar router!"
echo ""
echo "🔍 DISPOSITIVOS EN LA RED:"
if command -v nmap &> /dev/null; then
    echo "   Escaneando..."
    nmap -sn "$NETWORK" 2>/dev/null | grep "Nmap scan report" | sed 's/Nmap scan report for /   ✓ /'
else
    echo "   (Instala nmap para ver dispositivos)"
fi
echo ""
echo "✅ VENTAJAS DEL USO LOCAL:"
echo "   • Velocidad máxima (sin internet)"
echo "   • Latencia mínima"
echo "   • Sin configuración de router"
echo "   • Máxima privacidad"
echo ""
echo "=== FIN INFORMACIÓN ==="
EOF

chmod +x ~/.local/bin/rustdesk-local-info

# Script para alternativas remotas
cat > ~/.local/bin/rustdesk-remote-alternatives << 'EOF'
#!/bin/bash
# Alternativas para acceso remoto sin router

echo "=== ALTERNATIVAS PARA ACCESO REMOTO ==="
echo ""
echo "🚫 Sin acceso al router, pero tienes estas opciones:"
echo ""
echo "1. 🌐 NGROK (Túnel temporal - Gratuito con límites)"
echo "   Instalación:"
echo "   yay -S ngrok"
echo "   "
echo "   Uso:"
echo "   ngrok tcp 21115"
echo "   "
echo "   Resultado: URL como tcp://0.tcp.ngrok.io:12345"
echo "   Conecta desde cualquier lugar usando esa URL"
echo ""
echo "2. 🔗 TAILSCALE (VPN personal - Gratuito hasta 20 dispositivos)"
echo "   Instalación:"
echo "   yay -S tailscale"
echo "   sudo systemctl enable --now tailscaled"
echo "   sudo tailscale up"
echo "   "
echo "   Resultado: Todos tus dispositivos en red virtual"
echo "   Acceso directo usando IPs de Tailscale"
echo ""
echo "3. 🖥️  VPS PROPIO (Para uso intensivo)"
echo "   Requiere: Servidor VPS con IP pública"
echo "   Instalar RustDesk Server en el VPS"
echo "   Configurar todos los clientes para usar tu servidor"
echo ""
echo "4. 📱 APLICACIONES MÓVILES"
echo "   RustDesk tiene apps para Android/iOS"
echo "   Funciona perfecto en red local"
echo ""
echo "=== RECOMENDACIÓN ==="
echo "• Para uso ocasional remoto: NGROK"
echo "• Para uso frecuente remoto: TAILSCALE"
echo "• Para uso profesional: VPS propio"
echo "• Para uso doméstico: Red local (ya funciona)"
echo ""
echo "=== MÁS INFORMACIÓN ==="
echo "cat 10-rustdesk-sin-acceso-router.md"
EOF

chmod +x ~/.local/bin/rustdesk-remote-alternatives

# Script para instalar ngrok fácilmente
cat > ~/.local/bin/install-ngrok << 'EOF'
#!/bin/bash
# Instalador fácil de ngrok para RustDesk

echo "=== INSTALADOR NGROK PARA RUSTDESK ==="
echo ""
echo "Ngrok permite crear túneles temporales para acceso remoto"
echo "sin necesidad de configurar router."
echo ""
echo "¿Deseas instalar ngrok? (y/n)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "Instalando ngrok..."
    
    if yay -S ngrok --noconfirm; then
        echo ""
        echo "✅ Ngrok instalado correctamente"
        echo ""
        echo "=== CÓMO USAR ==="
        echo "1. Ejecuta: ngrok tcp 21115"
        echo "2. Copia la URL que aparece (ej: tcp://0.tcp.ngrok.io:12345)"
        echo "3. En el equipo remoto, conecta a esa URL"
        echo ""
        echo "=== LIMITACIONES GRATUITAS ==="
        echo "• Sesiones de 2 horas máximo"
        echo "• URL cambia cada vez"
        echo "• Ancho de banda limitado"
        echo ""
        echo "Para uso profesional considera la versión de pago."
    else
        echo "❌ Error instalando ngrok"
    fi
else
    echo "Instalación cancelada"
fi
EOF

chmod +x ~/.local/bin/install-ngrok

print_success "Scripts para red local creados"

# 6. Iniciar RustDesk optimizado
print_header "INICIANDO RUSTDESK OPTIMIZADO"
print_status "Iniciando RustDesk optimizado para red local..."

rustdesk &
RUSTDESK_PID=$!

sleep 3

if ps -p $RUSTDESK_PID > /dev/null; then
    print_success "RustDesk iniciado correctamente"
else
    print_warning "RustDesk puede haber tenido problemas al iniciar"
fi

# 7. Información final
print_header "CONFIGURACIÓN COMPLETADA"
echo ""
print_success "¡RustDesk optimizado para uso sin router!"
echo ""
echo "=== TU SITUACIÓN ACTUAL ==="
echo "✅ RustDesk funcionando perfectamente en red local"
echo "✅ Configuración optimizada para LAN"
echo "✅ Alternativas disponibles para uso remoto"
echo ""
echo "=== COMANDOS DISPONIBLES ==="
echo "• rustdesk-local-info         : Información de red local"
echo "• rustdesk-remote-alternatives : Ver alternativas remotas"
echo "• install-ngrok               : Instalar túnel ngrok"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo ""
echo "1. 📝 CONFIGURAR RUSTDESK:"
echo "   - Anota tu ID (aparece en pantalla)"
echo "   - Configura contraseña fija"
echo ""
echo "2. 🏠 PROBAR EN RED LOCAL:"
echo "   - Instala RustDesk en otro equipo de la casa"
echo "   - Conecta usando tu ID + contraseña"
echo "   - ¡Funciona sin configurar nada más!"
echo ""
echo "3. 🌐 PARA USO REMOTO (OPCIONAL):"
echo "   rustdesk-remote-alternatives"
echo ""
echo "=== VENTAJAS DE TU CONFIGURACIÓN ==="
echo "✅ Funciona perfectamente en red local"
echo "✅ Velocidad máxima (sin internet)"
echo "✅ Latencia mínima"
echo "✅ Sin dependencias externas"
echo "✅ Máxima privacidad"
echo "✅ Alternativas disponibles para remoto"
echo ""
print_success "¡RustDesk listo para uso local y remoto!"
