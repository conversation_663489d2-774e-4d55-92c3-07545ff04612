#!/bin/bash

# 00-actualizar-sistema-manjaro.sh
# Script para actualizar Manjaro con las mejores prácticas
# Preparación del sistema antes de instalar RustDesk

echo "=========================================="
echo "   ACTUALIZACIÓN SISTEMA MANJARO PLASMA"
echo "   Mejores prácticas antes de RustDesk"
echo "=========================================="
echo ""
echo "Este script realizará:"
echo "1. Sincronización de mirrors"
echo "2. Actualización completa del sistema"
echo "3. Limpieza de cache y paquetes huérfanos"
echo "4. Verificación de integridad"
echo "5. Instalación de herramientas esenciales"
echo ""
echo "¿Deseas continuar? (y/n)"
read -r CONTINUE

if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
    echo "Actualización cancelada por el usuario"
    exit 0
fi

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para verificar si el comando fue exitoso
check_command() {
    if [ $? -eq 0 ]; then
        print_success "$1 completado exitosamente"
    else
        print_error "Error en: $1"
        echo "¿Deseas continuar de todas formas? (y/n)"
        read -r CONTINUE_ERROR
        if [[ ! "$CONTINUE_ERROR" =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# PASO 1: Verificar conexión a internet
print_header "PASO 1: VERIFICACIÓN DE CONECTIVIDAD"
print_status "Verificando conexión a internet..."
ping -c 3 8.8.8.8 > /dev/null 2>&1
check_command "Verificación de conectividad"

# PASO 2: Actualizar mirrors de Manjaro
print_header "PASO 2: ACTUALIZACIÓN DE MIRRORS"
print_status "Actualizando lista de mirrors para mejor velocidad..."

# Instalar pacman-mirrors si no está disponible
if ! command -v pacman-mirrors &> /dev/null; then
    print_status "Instalando pacman-mirrors..."
    sudo pacman -S --needed pacman-mirrors --noconfirm
fi

# Actualizar mirrors automáticamente
print_status "Generando nueva lista de mirrors optimizada..."
sudo pacman-mirrors --fasttrack 5 --timeout 2
check_command "Actualización de mirrors"

# PASO 3: Sincronizar bases de datos
print_header "PASO 3: SINCRONIZACIÓN DE BASES DE DATOS"
print_status "Sincronizando bases de datos de paquetes..."
sudo pacman -Syy
check_command "Sincronización de bases de datos"

# PASO 4: Actualización completa del sistema
print_header "PASO 4: ACTUALIZACIÓN COMPLETA DEL SISTEMA"
print_status "Actualizando todos los paquetes del sistema..."
print_warning "Esto puede tomar varios minutos dependiendo de tu conexión"
sudo pacman -Syu --noconfirm
check_command "Actualización del sistema"

# PASO 5: Instalar yay si no está disponible
print_header "PASO 5: VERIFICACIÓN DE YAY (AUR HELPER)"
if ! command -v yay &> /dev/null; then
    print_status "Instalando yay para acceso a AUR..."
    cd /tmp
    sudo pacman -S --needed git base-devel --noconfirm
    git clone https://aur.archlinux.org/yay.git
    cd yay
    makepkg -si --noconfirm
    cd ~
    check_command "Instalación de yay"
else
    print_success "yay ya está instalado"
fi

# PASO 6: Actualizar paquetes AUR
print_header "PASO 6: ACTUALIZACIÓN DE PAQUETES AUR"
if command -v yay &> /dev/null; then
    print_status "Actualizando paquetes de AUR..."
    yay -Syu --noconfirm
    check_command "Actualización de paquetes AUR"
else
    print_warning "yay no disponible, saltando actualización AUR"
fi

# PASO 7: Limpieza de cache
print_header "PASO 7: LIMPIEZA DE CACHE"
print_status "Limpiando cache de paquetes..."

# Limpiar cache de pacman (mantener solo las 3 versiones más recientes)
sudo paccache -rk3
check_command "Limpieza de cache de pacman"

# Limpiar cache de yay si está disponible
if command -v yay &> /dev/null; then
    yay -Sc --noconfirm
    check_command "Limpieza de cache de yay"
fi

# PASO 8: Eliminar paquetes huérfanos
print_header "PASO 8: ELIMINACIÓN DE PAQUETES HUÉRFANOS"
print_status "Buscando paquetes huérfanos..."

ORPHANS=$(pacman -Qtdq)
if [ -n "$ORPHANS" ]; then
    print_status "Eliminando paquetes huérfanos encontrados:"
    echo "$ORPHANS"
    sudo pacman -Rns $ORPHANS --noconfirm
    check_command "Eliminación de paquetes huérfanos"
else
    print_success "No se encontraron paquetes huérfanos"
fi

# PASO 9: Verificar integridad del sistema
print_header "PASO 9: VERIFICACIÓN DE INTEGRIDAD"
print_status "Verificando integridad de archivos del sistema..."
sudo pacman -Qkk > /tmp/pacman_check.log 2>&1
BROKEN_FILES=$(grep -c "warning\|error" /tmp/pacman_check.log)
if [ "$BROKEN_FILES" -gt 0 ]; then
    print_warning "Se encontraron $BROKEN_FILES advertencias/errores"
    print_status "Revisa /tmp/pacman_check.log para detalles"
else
    print_success "Integridad del sistema verificada"
fi

# PASO 10: Instalar herramientas esenciales para RustDesk
print_header "PASO 10: INSTALACIÓN DE HERRAMIENTAS ESENCIALES"
print_status "Instalando dependencias y herramientas necesarias..."

ESSENTIAL_PACKAGES=(
    "base-devel"
    "git"
    "wget"
    "curl"
    "net-tools"
    "htop"
    "neofetch"
    "ufw"
    "cmake"
    "clang"
    "rust"
    "gtk3"
    "libxcb"
    "alsa-lib"
    "pulseaudio"
    "gstreamer"
    "gst-plugins-base"
    "libvpx"
    "opus"
)

for package in "${ESSENTIAL_PACKAGES[@]}"; do
    print_status "Instalando $package..."
    sudo pacman -S --needed "$package" --noconfirm 2>/dev/null || print_warning "No se pudo instalar $package"
done

# PASO 11: Configurar firewall básico
print_header "PASO 11: CONFIGURACIÓN BÁSICA DE FIREWALL"
print_status "Configurando UFW (firewall)..."
sudo ufw --force enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
print_success "Firewall configurado (se abrirán puertos específicos para RustDesk más tarde)"

# PASO 12: Optimizaciones del sistema
print_header "PASO 12: OPTIMIZACIONES DEL SISTEMA"
print_status "Aplicando optimizaciones menores..."

# Habilitar servicios importantes
sudo systemctl enable NetworkManager
sudo systemctl enable bluetooth

# Configurar swappiness para mejor rendimiento
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf > /dev/null

print_success "Optimizaciones aplicadas"

# PASO 13: Información del sistema actualizado
print_header "PASO 13: INFORMACIÓN DEL SISTEMA"
print_status "Mostrando información del sistema actualizado..."

echo ""
echo "=== INFORMACIÓN DEL SISTEMA ==="
neofetch --stdout 2>/dev/null || (
    echo "Distribución: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    echo "Kernel: $(uname -r)"
    echo "Arquitectura: $(uname -m)"
    echo "Memoria: $(free -h | grep Mem | awk '{print $2}')"
)

echo ""
echo "=== ESPACIO EN DISCO ==="
df -h / | tail -1

echo ""
echo "=== PAQUETES INSTALADOS ==="
echo "Total de paquetes: $(pacman -Q | wc -l)"
if command -v yay &> /dev/null; then
    echo "Paquetes AUR: $(yay -Qm | wc -l)"
fi

# RESUMEN FINAL
print_header "ACTUALIZACIÓN COMPLETADA"
echo ""
print_success "¡Sistema Manjaro actualizado exitosamente!"
echo ""
echo "=== RESUMEN DE ACCIONES REALIZADAS ==="
echo "✓ Mirrors optimizados para mejor velocidad"
echo "✓ Sistema completamente actualizado"
echo "✓ Cache limpiado y optimizado"
echo "✓ Paquetes huérfanos eliminados"
echo "✓ Integridad del sistema verificada"
echo "✓ Herramientas esenciales instaladas"
echo "✓ Firewall configurado"
echo "✓ Optimizaciones aplicadas"
echo ""
echo "=== PRÓXIMO PASO ==="
echo "El sistema está listo para la instalación de RustDesk"
echo "Ejecuta: ./01-eliminar-rustdesk-completo.sh"
echo ""
print_warning "RECOMENDACIÓN: Reinicia el sistema antes de instalar RustDesk"
echo "sudo reboot"
echo ""
print_success "¡Sistema preparado con las mejores prácticas!"
