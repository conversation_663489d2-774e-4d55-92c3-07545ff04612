# 07 - RustDesk sin Servidores Oficiales

## 🚫 **PROBLEMA IDENTIFICADO**

Los servidores oficiales de RustDesk (`admin.rustdesk.com`, `relay.rustdesk.com`) **están bloqueados** en tu red. Esto es común debido a:

- **ISP que bloquea servicios P2P**
- **Firewall corporativo/institucional**
- **Restricciones geográficas**
- **Políticas de red locales**

## ✅ **SOLUCIONES DISPONIBLES**

### **Opción 1: Uso Tradicional (SIN CUENTA) - RECOMENDADO**

Esta es la **mejor opción** cuando los servidores están bloqueados:

#### **Cómo funciona:**
1. RustDesk genera un **ID único** para tu equipo
2. Configuras una **contraseña fija**
3. Desde otro equipo te conectas usando **ID + contraseña**
4. **No necesita servidores externos** para conexiones directas

#### **Pasos para configurar:**

1. **Abrir RustDesk:**
   ```bash
   rustdesk
   ```

2. **Anotar tu ID:**
   - Aparece en la ventana principal (ej: `123456789`)
   - **Guárdalo bien**, lo necesitarás para conectarte

3. **Configurar contraseña fija:**
   - Clic en el ícono de "Configuración" (engranaje)
   - Ir a "Seguridad" → "Contraseña"
   - Establecer una contraseña fuerte
   - **Desmarcar** "Contraseña temporal"

4. **Configurar acceso remoto:**
   - En tu **router**, configura **Port Forwarding**:
     - Puertos: `21115-21119` (TCP) y `21116` (UDP)
     - IP destino: IP local de este equipo
   - O usa **UPnP** si está disponible

#### **Ventajas:**
- ✅ **Funciona sin servidores externos**
- ✅ **Conexión directa** (mejor rendimiento)
- ✅ **Mayor privacidad** (no pasa por terceros)
- ✅ **No depende de cuentas**

#### **Desventajas:**
- ❌ Requiere configurar router
- ❌ Necesitas recordar IDs
- ❌ Sin sincronización entre dispositivos

---

### **Opción 2: Servidor Propio RustDesk**

Si quieres funciones avanzadas, puedes instalar tu propio servidor:

#### **Requisitos:**
- VPS o servidor con IP pública
- Conocimientos básicos de administración

#### **Pasos básicos:**
```bash
# En tu VPS
docker run --name hbbs -p 21115:21115 -p 21116:21116 -p 21116:21116/udp -p 21118:21118 -v ./data:/root rustdesk/rustdesk-server hbbs -r <tu-ip-publica>

docker run --name hbbr -p 21117:21117 -p 21119:21119 -v ./data:/root rustdesk/rustdesk-server hbbr
```

#### **Configurar clientes:**
- En RustDesk → Configuración → Red
- Servidor ID: `tu-ip-publica`
- Clave: (generar desde servidor)

---

### **Opción 3: VPN + Servidores Oficiales**

Si necesitas usar los servidores oficiales:

#### **VPNs recomendadas:**
- **ProtonVPN** (gratuito limitado)
- **Windscribe** (10GB gratis)
- **TunnelBear** (500MB gratis)

#### **Pasos:**
1. Instalar VPN
2. Conectar a servidor en país sin restricciones
3. Usar RustDesk normalmente

---

## 🎯 **RECOMENDACIÓN PARA TU CASO**

### **USA LA OPCIÓN 1 (Tradicional)**

Es la **mejor opción** porque:
- ✅ **Funciona inmediatamente**
- ✅ **No requiere VPN ni servidores**
- ✅ **Mejor rendimiento** (conexión directa)
- ✅ **Más seguro** (no pasa por terceros)

## 📋 **GUÍA PASO A PASO - MÉTODO TRADICIONAL**

### **1. Configurar RustDesk:**

```bash
# Iniciar RustDesk
rustdesk
```

**En la interfaz:**
1. **Anota tu ID** (aparece en pantalla principal)
2. Clic en **⚙️ Configuración**
3. Ir a **"Seguridad"**
4. Establecer **contraseña fija**
5. **Desmarcar** "Usar contraseña temporal"

### **2. Configurar Router (Port Forwarding):**

**Acceder al router:**
```bash
# Encontrar IP del router
ip route | grep default

# Acceder vía navegador
# http://*********** (o la IP que aparezca)
```

**Configurar puertos:**
- **Protocolo:** TCP
- **Puertos externos:** 21115-21119
- **IP interna:** IP de este equipo
- **Puertos internos:** 21115-21119

**También UDP:**
- **Protocolo:** UDP  
- **Puerto externo:** 21116
- **IP interna:** IP de este equipo
- **Puerto interno:** 21116

### **3. Obtener IP pública:**

```bash
# Ver tu IP pública
curl ifconfig.me
```

### **4. Conectar desde otro equipo:**

1. Instalar RustDesk en el otro equipo
2. En "ID del Socio" poner: **TU_ID_RUSTDESK**
3. Conectar
4. Introducir la contraseña que configuraste

---

## 🔧 **SCRIPT AUTOMÁTICO PARA CONFIGURACIÓN**

He creado un script que automatiza la configuración:

```bash
# Ejecutar script de configuración tradicional
./08-configurar-rustdesk-tradicional.sh
```

---

## ❓ **PREGUNTAS FRECUENTES**

### **¿Por qué están bloqueados los servidores?**
- Políticas del ISP contra servicios P2P
- Restricciones corporativas/institucionales
- Bloqueos geográficos

### **¿Es seguro el método tradicional?**
- **SÍ**, incluso más seguro que usar servidores externos
- La conexión es directa entre tus equipos
- No pasa por servidores de terceros

### **¿Funciona desde cualquier lugar?**
- **SÍ**, mientras tengas internet y los puertos abiertos
- No depende de servidores externos

### **¿Puedo usar ambos métodos?**
- **SÍ**, puedes cambiar entre métodos según necesites

---

## 🎉 **CONCLUSIÓN**

**No te preocupes por el bloqueo de servidores.** El método tradicional de RustDesk es:
- ✅ **Más rápido** (conexión directa)
- ✅ **Más privado** (sin terceros)
- ✅ **Más confiable** (no depende de servidores externos)

**Próximo paso:** Ejecuta el script de configuración tradicional y tendrás RustDesk funcionando perfectamente para uso remoto.
