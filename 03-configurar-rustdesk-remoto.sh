#!/bin/bash

# 03-configurar-rustdesk-remoto.sh
# Script para configurar RustDesk específicamente para uso remoto
# Optimiza configuraciones y desactiva funciones locales innecesarias

echo "=== CONFIGURACIÓN AVANZADA RUSTDESK REMOTO ==="
echo "Este script optimizará RustDesk para uso exclusivamente remoto"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar que RustDesk esté instalado
if ! command -v rustdesk &> /dev/null; then
    print_error "RustDesk no está instalado. Ejecuta primero 02-instalar-rustdesk-mejores-practicas.sh"
    exit 1
fi

# 2. Detener RustDesk si está ejecutándose
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true

# 3. Crear configuración avanzada
print_status "Creando configuración avanzada..."
mkdir -p ~/.config/rustdesk

# Configuración completa para uso remoto
cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk optimizada para uso REMOTO únicamente
# Generada automáticamente - NO editar manualmente

[options]
# === CONFIGURACIÓN DE RED ===
# Desactivar descubrimiento LAN (no necesario para remoto)
enable-lan-discovery = false
enable-direct-ip-access = true
enable-tunnel = true
enable-tcp = true
enable-udp = true

# Servidores (usar por defecto de RustDesk)
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# === OPTIMIZACIONES DE RENDIMIENTO ===
# Habilitar aceleración por hardware
enable-hardware-codec = true
enable-gpu = true

# Códecs optimizados
video-codec = "VP9"  # Mejor compresión
audio-codec = "Opus" # Mejor calidad de audio

# Calidad de imagen
image-quality = "Balanced" # Balanced, Best, Low

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# Bloquear entrada local cuando hay conexión remota
block-input-when-minimized = false
enable-remove-wallpaper = false

# === CONFIGURACIÓN DE INTERFAZ ===
# Tema oscuro para mejor experiencia
theme = "dark"

# Configuraciones de ventana
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
enable-minimize-to-tray = true

# === LOGGING Y DEPURACIÓN ===
# Logging mínimo para mejor rendimiento
log-level = "warn"
enable-file-logging = false

# === CONFIGURACIONES ESPECÍFICAS PARA REMOTO ===
# Desactivar funciones no necesarias para uso remoto
enable-lan-discovery = false
enable-upnp = false
enable-local-relay = false

# Optimizar para conexiones remotas
enable-direct-relay = true
enable-tcp-tunneling = true
EOF

# 4. Crear script de inicio optimizado
print_status "Creando script de inicio optimizado..."
mkdir -p ~/.local/bin

cat > ~/.local/bin/rustdesk-remote << 'EOF'
#!/bin/bash
# Script optimizado para RustDesk remoto

# Variables de entorno para mejor rendimiento
export RUSTDESK_LOG_LEVEL=warn
export RUSTDESK_DISABLE_LAN=1

# Iniciar RustDesk
rustdesk "$@"
EOF

chmod +x ~/.local/bin/rustdesk-remote

# 5. Configurar autostart (opcional)
print_status "¿Deseas que RustDesk se inicie automáticamente? (y/n)"
read -r AUTO_START

if [[ "$AUTO_START" =~ ^[Yy]$ ]]; then
    mkdir -p ~/.config/autostart
    cat > ~/.config/autostart/rustdesk.desktop << EOF
[Desktop Entry]
Type=Application
Name=RustDesk Remote
Comment=Remote Desktop Client
Exec=$HOME/.local/bin/rustdesk-remote --service
Icon=rustdesk
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
StartupNotify=false
EOF
    print_success "Autostart configurado"
fi

# 6. Crear script de diagnóstico
print_status "Creando script de diagnóstico..."
cat > ~/.local/bin/rustdesk-diagnostics << 'EOF'
#!/bin/bash
# Script de diagnóstico para RustDesk

echo "=== DIAGNÓSTICO RUSTDESK ==="
echo "Fecha: $(date)"
echo ""

echo "1. Estado de instalación:"
if command -v rustdesk &> /dev/null; then
    echo "   ✓ RustDesk: Instalado"
    rustdesk --version 2>/dev/null || echo "   - Versión no disponible"
else
    echo "   ✗ RustDesk no encontrado"
fi

echo ""
echo "2. Configuración:"
if [ -f ~/.config/rustdesk/RustDesk2.toml ]; then
    echo "   ✓ Archivo de configuración existe"
else
    echo "   ✗ Archivo de configuración no encontrado"
fi

echo ""
echo "3. Red:"
echo "   Puertos RustDesk:"
netstat -tuln 2>/dev/null | grep -E "(21115|21116|21117|21118|21119)" || echo "   - No hay puertos RustDesk abiertos"

echo ""
echo "4. Procesos:"
pgrep -f rustdesk >/dev/null && echo "   ✓ RustDesk ejecutándose" || echo "   - RustDesk no está ejecutándose"

echo ""
echo "5. Firewall:"
if command -v ufw &> /dev/null && sudo ufw status | grep -q "Status: active"; then
    sudo ufw status | grep -E "(21115|21116)" && echo "   ✓ Reglas UFW configuradas" || echo "   - Sin reglas UFW específicas"
else
    echo "   - UFW no activo o no disponible"
fi

echo ""
echo "=== FIN DIAGNÓSTICO ==="
EOF

chmod +x ~/.local/bin/rustdesk-diagnostics

# 7. Información final
print_success "Configuración avanzada completada"
echo ""
echo "=== COMANDOS DISPONIBLES ==="
echo "• rustdesk-remote          : Iniciar RustDesk optimizado"
echo "• rustdesk-diagnostics     : Ejecutar diagnóstico"
echo ""
echo "=== ARCHIVOS CREADOS ==="
echo "• ~/.config/rustdesk/RustDesk2.toml    : Configuración principal"
echo "• ~/.local/bin/rustdesk-remote         : Script de inicio"
echo "• ~/.local/bin/rustdesk-diagnostics    : Script de diagnóstico"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo "1. Ejecuta: rustdesk-remote"
echo "2. Configura tu ID y contraseña"
echo "3. Si quieres usar 'Iniciar Sesión', lee 04-guia-iniciar-sesion-rustdesk.md"
echo "4. Usa rustdesk-diagnostics si tienes problemas"
echo ""
print_success "¡RustDesk configurado para uso remoto óptimo!"

# 02-configurar-rustdesk-remoto.sh
# Script para configurar RustDesk específicamente para uso remoto
# Optimiza configuraciones y desactiva funciones locales innecesarias

echo "=== CONFIGURACIÓN AVANZADA RUSTDESK REMOTO ==="
echo "Este script optimizará RustDesk para uso exclusivamente remoto"
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar que RustDesk esté instalado
if ! command -v rustdesk &> /dev/null && ! flatpak list | grep -q rustdesk && [ ! -f ~/Applications/rustdesk.AppImage ]; then
    print_error "RustDesk no está instalado. Ejecuta primero 01-instalar-rustdesk-mejores-practicas.sh"
    exit 1
fi

# 2. Detener RustDesk si está ejecutándose
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true

# 3. Crear configuración avanzada
print_status "Creando configuración avanzada..."
mkdir -p ~/.config/rustdesk

# Configuración completa para uso remoto
cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk optimizada para uso REMOTO únicamente
# Generada automáticamente - NO editar manualmente

[options]
# === CONFIGURACIÓN DE RED ===
# Desactivar descubrimiento LAN (no necesario para remoto)
enable-lan-discovery = false
enable-direct-ip-access = true
enable-tunnel = true
enable-tcp = true
enable-udp = true

# Servidores (usar por defecto de RustDesk)
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# === OPTIMIZACIONES DE RENDIMIENTO ===
# Habilitar aceleración por hardware
enable-hardware-codec = true
enable-gpu = true

# Códecs optimizados
video-codec = "VP9"  # Mejor compresión
audio-codec = "Opus" # Mejor calidad de audio

# Calidad de imagen
image-quality = "Balanced" # Balanced, Best, Low

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# Bloquear entrada local cuando hay conexión remota
block-input-when-minimized = false
enable-remove-wallpaper = false

# === CONFIGURACIÓN DE INTERFAZ ===
# Tema oscuro para mejor experiencia
theme = "dark"

# Configuraciones de ventana
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true
enable-minimize-to-tray = true

# === LOGGING Y DEPURACIÓN ===
# Logging mínimo para mejor rendimiento
log-level = "warn"
enable-file-logging = false

# === CONFIGURACIONES ESPECÍFICAS PARA REMOTO ===
# Desactivar funciones no necesarias para uso remoto
enable-lan-discovery = false
enable-upnp = false
enable-local-relay = false

# Optimizar para conexiones remotas
enable-direct-relay = true
enable-tcp-tunneling = true
EOF

# 4. Configurar variables de entorno
print_status "Configurando variables de entorno..."
cat > ~/.config/rustdesk/env.conf << 'EOF'
# Variables de entorno para RustDesk remoto
RUSTDESK_LOG_LEVEL=warn
RUSTDESK_DISABLE_LAN=1
RUSTDESK_REMOTE_ONLY=1
EOF

# 5. Crear script de inicio optimizado
print_status "Creando script de inicio optimizado..."
mkdir -p ~/.local/bin

cat > ~/.local/bin/rustdesk-remote << 'EOF'
#!/bin/bash
# Script optimizado para RustDesk remoto

# Cargar variables de entorno
if [ -f ~/.config/rustdesk/env.conf ]; then
    source ~/.config/rustdesk/env.conf
fi

# Detectar método de instalación y ejecutar
if command -v rustdesk &> /dev/null; then
    # Instalación AUR
    rustdesk "$@"
elif flatpak list | grep -q rustdesk; then
    # Instalación Flatpak
    flatpak run com.rustdesk.RustDesk "$@"
elif [ -f ~/Applications/rustdesk.AppImage ]; then
    # AppImage
    ~/Applications/rustdesk.AppImage "$@"
else
    echo "Error: RustDesk no encontrado"
    exit 1
fi
EOF

chmod +x ~/.local/bin/rustdesk-remote

# 6. Configurar autostart (opcional)
print_status "¿Deseas que RustDesk se inicie automáticamente? (y/n)"
read -r AUTO_START

if [[ "$AUTO_START" =~ ^[Yy]$ ]]; then
    mkdir -p ~/.config/autostart
    cat > ~/.config/autostart/rustdesk.desktop << EOF
[Desktop Entry]
Type=Application
Name=RustDesk Remote
Comment=Remote Desktop Client
Exec=$HOME/.local/bin/rustdesk-remote --service
Icon=rustdesk
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
StartupNotify=false
EOF
    print_success "Autostart configurado"
fi

# 7. Configurar acceso directo mejorado
print_status "Creando acceso directo optimizado..."
cat > ~/.local/share/applications/rustdesk-remote.desktop << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=RustDesk Remote
Comment=Remote Desktop optimizado para uso remoto
Exec=$HOME/.local/bin/rustdesk-remote
Icon=rustdesk
Terminal=false
Categories=Network;RemoteAccess;
Keywords=remote;desktop;vnc;rdp;
StartupWMClass=rustdesk
EOF

# 8. Crear script de diagnóstico
print_status "Creando script de diagnóstico..."
cat > ~/.local/bin/rustdesk-diagnostics << 'EOF'
#!/bin/bash
# Script de diagnóstico para RustDesk

echo "=== DIAGNÓSTICO RUSTDESK ==="
echo "Fecha: $(date)"
echo ""

echo "1. Estado de instalación:"
if command -v rustdesk &> /dev/null; then
    echo "   ✓ AUR: Instalado"
elif flatpak list | grep -q rustdesk; then
    echo "   ✓ Flatpak: Instalado"
elif [ -f ~/Applications/rustdesk.AppImage ]; then
    echo "   ✓ AppImage: Disponible"
else
    echo "   ✗ No encontrado"
fi

echo ""
echo "2. Configuración:"
if [ -f ~/.config/rustdesk/RustDesk2.toml ]; then
    echo "   ✓ Archivo de configuración existe"
else
    echo "   ✗ Archivo de configuración no encontrado"
fi

echo ""
echo "3. Red:"
echo "   Puertos RustDesk:"
netstat -tuln | grep -E "(21115|21116|21117|21118|21119)" || echo "   - No hay puertos RustDesk abiertos"

echo ""
echo "4. Procesos:"
pgrep -f rustdesk && echo "   ✓ RustDesk ejecutándose" || echo "   - RustDesk no está ejecutándose"

echo ""
echo "5. Firewall:"
if command -v ufw &> /dev/null; then
    sudo ufw status | grep -E "(21115|21116)" && echo "   ✓ Reglas UFW configuradas" || echo "   - Sin reglas UFW específicas"
fi

echo ""
echo "=== FIN DIAGNÓSTICO ==="
EOF

chmod +x ~/.local/bin/rustdesk-diagnostics

# 9. Información final
print_success "Configuración avanzada completada"
echo ""
echo "=== COMANDOS DISPONIBLES ==="
echo "• rustdesk-remote          : Iniciar RustDesk optimizado"
echo "• rustdesk-diagnostics     : Ejecutar diagnóstico"
echo ""
echo "=== ARCHIVOS CREADOS ==="
echo "• ~/.config/rustdesk/RustDesk2.toml    : Configuración principal"
echo "• ~/.config/rustdesk/env.conf          : Variables de entorno"
echo "• ~/.local/bin/rustdesk-remote         : Script de inicio"
echo "• ~/.local/bin/rustdesk-diagnostics    : Script de diagnóstico"
echo ""
echo "=== PRÓXIMOS PASOS ==="
echo "1. Ejecuta: rustdesk-remote"
echo "2. Configura tu ID y contraseña"
echo "3. Si quieres usar 'Iniciar Sesión', lee ~/rustdesk-login-info.txt"
echo "4. Usa rustdesk-diagnostics si tienes problemas"
echo ""
print_success "¡RustDesk configurado para uso remoto óptimo!"
