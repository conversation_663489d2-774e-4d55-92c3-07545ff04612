#!/bin/bash

# 04-proceso-completo-rustdesk.sh
# Script maestro para eliminar e instalar RustDesk con mejores prácticas
# Ejecuta todo el proceso de forma automatizada

echo "=========================================="
echo "    REINSTALACIÓN COMPLETA DE RUSTDESK"
echo "    Para Manjaro Plasma - Uso Remoto"
echo "=========================================="
echo ""
echo "Este script ejecutará todo el proceso:"
echo "1. Actualización completa del sistema Manjaro"
echo "2. Eliminación completa de RustDesk"
echo "3. Instalación con mejores prácticas"
echo "4. Configuración optimizada para uso remoto"
echo "5. Creación de scripts y herramientas"
echo ""
echo "¿Deseas continuar? (y/n)"
read -r CONTINUE

if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
    echo "Proceso cancelado por el usuario"
    exit 0
fi

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para pausar entre pasos
pause_step() {
    echo ""
    echo "Presiona Enter para continuar al siguiente paso..."
    read
    echo ""
}

# PASO 1: Actualización del sistema
print_header "PASO 1: ACTUALIZACIÓN DEL SISTEMA MANJARO"
print_status "Ejecutando script de actualización..."

if [ -f "00-actualizar-sistema-manjaro.sh" ]; then
    chmod +x 00-actualizar-sistema-manjaro.sh
    ./00-actualizar-sistema-manjaro.sh
    if [ $? -eq 0 ]; then
        print_success "Actualización del sistema completada exitosamente"
    else
        print_error "Error en la actualización del sistema"
        exit 1
    fi
else
    print_error "Script de actualización no encontrado"
    exit 1
fi

pause_step

# PASO 2: Eliminación completa
print_header "PASO 2: ELIMINACIÓN COMPLETA"
print_status "Ejecutando script de eliminación..."

if [ -f "01-eliminar-rustdesk-completo.sh" ]; then
    chmod +x 01-eliminar-rustdesk-completo.sh
    ./01-eliminar-rustdesk-completo.sh
    if [ $? -eq 0 ]; then
        print_success "Eliminación completada exitosamente"
    else
        print_error "Error en la eliminación"
        exit 1
    fi
else
    print_error "Script de eliminación no encontrado"
    exit 1
fi

pause_step

# PASO 3: Instalación con mejores prácticas
print_header "PASO 3: INSTALACIÓN CON MEJORES PRÁCTICAS"
print_status "Ejecutando script de instalación..."

if [ -f "02-instalar-rustdesk-mejores-practicas.sh" ]; then
    chmod +x 02-instalar-rustdesk-mejores-practicas.sh
    ./02-instalar-rustdesk-mejores-practicas.sh
    if [ $? -eq 0 ]; then
        print_success "Instalación completada exitosamente"
    else
        print_error "Error en la instalación"
        exit 1
    fi
else
    print_error "Script de instalación no encontrado"
    exit 1
fi

pause_step

# PASO 4: Configuración avanzada
print_header "PASO 4: CONFIGURACIÓN AVANZADA"
print_status "Ejecutando script de configuración..."

if [ -f "03-configurar-rustdesk-remoto.sh" ]; then
    chmod +x 03-configurar-rustdesk-remoto.sh
    ./03-configurar-rustdesk-remoto.sh
    if [ $? -eq 0 ]; then
        print_success "Configuración completada exitosamente"
    else
        print_error "Error en la configuración"
        exit 1
    fi
else
    print_error "Script de configuración no encontrado"
    exit 1
fi

# PASO 5: Verificación final
print_header "PASO 5: VERIFICACIÓN FINAL"
print_status "Ejecutando diagnóstico..."

if command -v rustdesk-diagnostics &> /dev/null; then
    rustdesk-diagnostics
else
    print_warning "Script de diagnóstico no disponible aún"
fi

# PASO 5: Resumen final
print_header "PROCESO COMPLETADO"
echo ""
print_success "¡RustDesk ha sido reinstalado exitosamente!"
echo ""
echo "=== RESUMEN DE LO REALIZADO ==="
echo "✓ Sistema Manjaro actualizado completamente"
echo "✓ Eliminación completa de instalación anterior"
echo "✓ Instalación limpia con mejores prácticas"
echo "✓ Configuración optimizada para uso remoto"
echo "✓ Scripts y herramientas de diagnóstico creados"
echo ""
echo "=== ARCHIVOS DISPONIBLES ==="
echo "• 00-actualizar-sistema-manjaro.sh     : Script de actualización del sistema"
echo "• 01-eliminar-rustdesk-completo.sh     : Script de eliminación"
echo "• 02-instalar-rustdesk-mejores-practicas.sh : Script de instalación"
echo "• 03-configurar-rustdesk-remoto.sh     : Script de configuración"
echo "• 04-guia-iniciar-sesion-rustdesk.md   : Guía sobre 'Iniciar Sesión'"
echo "• 05-proceso-completo-rustdesk.sh      : Este script maestro"
echo ""
echo "=== COMANDOS DISPONIBLES ==="
echo "• rustdesk-remote          : Iniciar RustDesk optimizado"
echo "• rustdesk-diagnostics     : Ejecutar diagnóstico del sistema"
echo ""
echo "=== PRÓXIMOS PASOS RECOMENDADOS ==="
echo ""
echo "1. 📖 LEE LA GUÍA:"
echo "   cat 03-guia-iniciar-sesion-rustdesk.md"
echo ""
echo "2. 🚀 INICIA RUSTDESK:"
echo "   rustdesk-remote"
echo ""
echo "3. ⚙️  CONFIGURA TU ID Y CONTRASEÑA:"
echo "   - Anota tu ID para acceso remoto"
echo "   - Establece una contraseña segura"
echo ""
echo "4. 🔐 DECIDE SOBRE 'INICIAR SESIÓN':"
echo "   - Lee la guía para entender los beneficios"
echo "   - Recomendado para uso remoto"
echo ""
echo "5. 🔧 SI TIENES PROBLEMAS:"
echo "   rustdesk-diagnostics"
echo ""
echo "=== CONFIGURACIÓN APLICADA ==="
echo "• Optimizado para uso REMOTO únicamente"
echo "• Desactivado descubrimiento LAN"
echo "• Habilitada aceleración por hardware"
echo "• Códecs optimizados (VP9 + Opus)"
echo "• Logging mínimo para mejor rendimiento"
echo "• Firewall configurado automáticamente"
echo ""
print_success "¡Disfruta de tu RustDesk optimizado!"
echo ""
echo "Tip: Guarda este directorio como referencia para futuras reinstalaciones"
